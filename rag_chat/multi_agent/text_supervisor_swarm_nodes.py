"""
文本统一图的supervisor和swarm节点实现

基于LangGraph官方API风格的文本模式supervisor和swarm节点。
"""

import asyncio
from typing import Any, Dict, List, Optional
from langchain_core.messages import AIMessage
from langchain_openai import ChatOpenAI

from .text_supervisor_swarm import create_text_supervisor, create_text_swarm, create_text_handoff_tool
from .agents.text_simple_chat_agent import create_text_simple_chat_agent
from .agents.text_knowledge_agent import create_text_knowledge_agent
from .agents.text_mcp_agent import create_text_mcp_agent
from .agents.text_multimodal_agent import create_text_multimodal_agent
# ReAct Agent导入
from .agents.text_knowledge_agent_react import create_text_knowledge_agent_react
from .agents.text_mcp_agent_react import create_text_mcp_agent_react
from .agents.text_multimodal_agent_react import create_text_multimodal_agent_react
from .agents.text_custom_rag_agent_react import create_text_custom_rag_agent_react

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from logger import logger
from jiliang_chat_prompt import SUPERVISOR_AGENT_PROMPT


class TextSupervisorSwarmNodes:
    """文本模式的supervisor和swarm节点实现"""
    
    def __init__(self, 
                 mcp_router: Any = None,
                 mcp_simple_router: Any = None,
                 checkpointer: Any = None):
        """初始化节点处理器
        
        Args:
            mcp_router: MCP RAG路由器
            mcp_simple_router: MCP简单路由器
            checkpointer: 检查点保存器
        """
        self.mcp_router = mcp_router
        self.mcp_simple_router = mcp_simple_router
        self.checkpointer = checkpointer
    
    async def text_supervisor_node(self, state: Any) -> Any:
        """文本模式监督者协调节点"""
        
        try:
            logger.info("执行文本模式监督者协调")
            
            # 创建需要协调的Agent
            agents = []
            
            # 主要Agent
            primary_agent = await self._create_text_agent(state.primary_agent, state)
            agents.append(primary_agent)
            
            # 次要Agent
            for agent_type in state.secondary_agents:
                if agent_type != state.primary_agent:
                    agent = await self._create_text_agent(agent_type, state)
                    agents.append(agent)
            
            # 创建文本模式监督者
            supervisor_model = os.getenv("SUPERVISOR_MODEL", "qwq32b")
            llm = self._create_llm(extra_headers=state.extra_headers, model_name=supervisor_model, is_reasoning=True, agent_name="supervisor")
            supervisor_prompt = SUPERVISOR_AGENT_PROMPT.format(model_desc=state.model_desc)
            
            supervisor_workflow = create_text_supervisor(
                agents=agents,
                model=llm,
                prompt=supervisor_prompt,
                output_mode="last_message",
                include_agent_name=getattr(state, 'include_agent_name', "inline"),  # 使用状态中的设置
                filter_agent_messages=getattr(state, 'filter_agent_messages', False)
            )
            
            # 编译并执行
            supervisor_graph = supervisor_workflow.compile(checkpointer=self.checkpointer)
            
            config = self._build_agent_config(state, "text_supervisor")
            result = await supervisor_graph.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            if result and "messages" in result:
                new_messages = result.get("messages", [])
                # 只添加新的消息，避免重复
                existing_count = len(state.messages)
                if len(new_messages) > existing_count:
                    to_add = new_messages[existing_count:]
                else:
                    to_add = new_messages
                
                # 根据过滤设置决定要添加的消息
                if getattr(state, 'filter_agent_messages', False):
                    # 只返回最后一条AI消息（类似LangGraph的last_message模式）
                    filtered_messages = []
                    for msg in reversed(to_add):
                        if hasattr(msg, 'content') and isinstance(msg, AIMessage):
                            filtered_messages = [msg]
                            break
                    state.messages.extend(filtered_messages)
                    logger.info(f"Supervisor消息过滤：从{len(to_add)}条消息过滤到{len(filtered_messages)}条")
                else:
                    # 返回全部执行流程
                    state.messages.extend(to_add)
            
            state.agent_results["text_supervisor"] = result
            state.execution_status = "completed"
            
            logger.info("文本模式监督者协调完成")
            return state
            
        except Exception as e:
            logger.error(f"文本模式监督者协调失败: {e}")
            error_msg = AIMessage(content=f"监督者协调时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def text_parallel_coordinator_node(self, state: Any) -> Any:
        """文本模式并行协调节点（基于Swarm）"""
        
        try:
            logger.info("执行文本模式Swarm智能并行协调")
            
            # 准备需要协调的Agent列表
            agents_to_run = [state.primary_agent] + state.secondary_agents
            swarm_agents = []
            
            # 创建Agent并添加handoff工具
            for agent_type in agents_to_run:
                # 创建handoff工具列表，允许Agent相互协调
                handoff_tools = []
                for other_agent in agents_to_run:
                    if other_agent != agent_type:
                        handoff_tool = create_text_handoff_tool(
                            agent_name=other_agent,
                            description=f"将任务转交给{self._get_agent_description(other_agent)}处理相关问题"
                        )
                        handoff_tools.append(handoff_tool)
                
                # 创建增强的Agent（包含handoff工具）
                enhanced_agent = await self._create_text_agent(
                    agent_type, state, handoff_tools
                )
                swarm_agents.append(enhanced_agent)
            
            # 创建文本模式Swarm工作流
            swarm_workflow = create_text_swarm(
                agents=swarm_agents,
                default_active_agent=state.primary_agent,
                filter_agent_messages=getattr(state, 'filter_agent_messages', False),
                include_agent_name=getattr(state, 'include_agent_name', "inline")  # 默认使用inline模式
            )
            
            # 编译Swarm图，支持checkpointer
            swarm_graph = swarm_workflow.compile(checkpointer=self.checkpointer)
            
            # 执行Swarm协调
            config = self._build_agent_config(state, "text_swarm")
            result = await swarm_graph.ainvoke(
                {"messages": state.messages},
                config=config
            )
            
            # 更新状态
            new_messages = result.get("messages", [])
            
            # 根据过滤设置决定要添加的消息
            if getattr(state, 'filter_agent_messages', False):
                # 只返回最后一条AI消息（类似LangGraph的last_message模式）
                filtered_messages = []
                for msg in reversed(new_messages):
                    if hasattr(msg, 'content') and isinstance(msg, AIMessage):
                        filtered_messages = [msg]
                        break
                state.messages.extend(filtered_messages)
                logger.info(f"Swarm消息过滤：从{len(new_messages)}条消息过滤到{len(filtered_messages)}条")
            else:
                # 返回全部执行流程
                state.messages.extend(new_messages)
            
            state.agent_results["text_swarm_coordinator"] = result
            state.execution_status = "completed"
            
            logger.info(f"文本模式Swarm智能协调完成，协调了{len(swarm_agents)}个Agent")
            return state
            
        except Exception as e:
            logger.error(f"文本模式Swarm智能协调失败: {e}")
            error_msg = AIMessage(content=f"Swarm智能协调时遇到问题：{str(e)}")
            state.messages.append(error_msg)
            state.execution_status = "failed"
            return state
    
    async def _create_text_agent(self, agent_type: str, 
                                 state: Any, 
                                 handoff_tools: Optional[List[Any]] = None) -> Any:
        """创建文本模式的单个Agent实例（支持ReAct模式）"""
        
        # 根据agent类型选择模型
        if agent_type in ["knowledge_agent", "custom_rag_agent"]:
            model_name = os.getenv("KNOWLEDGE_MODEL", "qwen2---5-72b-goxmbugy")
        elif agent_type == "mcp_agent":
            model_name = os.getenv("TOOL_MODEL", "qwen2---5-72b-goxmbugy")
        elif agent_type == "multimodal_agent":
            model_name = os.getenv("MULTIMODAL_MODEL", "qwen2-5-vl-72b")
        else:
            model_name = os.getenv("CHAT_MODEL", "qwen2---5-72b-goxmbugy")

        llm = self._create_llm(extra_headers=state.extra_headers, model_name=model_name, agent_name=agent_type)
        
        if handoff_tools is None:
            handoff_tools = []
        
        # 检查是否使用ReAct模式
        use_react_mode = getattr(state, 'execution_mode', 'text_parsing') == 'react_mode' and getattr(state, 'use_react_agents', True)
        max_iterations = getattr(state, 'max_iterations', 10)
        
        if agent_type == "knowledge_agent":
            if use_react_mode:
                agent = await create_text_knowledge_agent_react(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer
                )
            else:
                agent = await create_text_knowledge_agent(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    checkpointer=self.checkpointer
                )
            agent.name = "knowledge_agent"  # 设置名称用于映射
            return agent
        
        elif agent_type == "simple_chat":
            # 简单聊天不需要ReAct模式
            agent = await create_text_simple_chat_agent(
                llm=llm,
                model_desc=state.model_desc,
                handoff_tools=handoff_tools,
                checkpointer=self.checkpointer,
                enable_context_summarization=True
            )
            agent.name = "simple_chat"  # 设置名称用于映射
            return agent
            
        elif agent_type == "mcp_agent":
            if use_react_mode:
                agent = await create_text_mcp_agent_react(
                    llm=llm,
                    question=state.current_question,
                    mcp_ids=state.mcp_ids,
                    model_desc=state.model_desc,
                    mcp_router=self.mcp_router,
                    mcp_simple_router=self.mcp_simple_router,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer,
                    api_key=getattr(state.extra_headers, 'api_key', None),
                    username=getattr(state.extra_headers, 'username', None)
                )
            else:
                agent = await create_text_mcp_agent(
                    llm=llm,
                    question=state.current_question,
                    mcp_ids=state.mcp_ids,
                    model_desc=state.model_desc,
                    mcp_router=self.mcp_router,
                    mcp_simple_router=self.mcp_simple_router,
                    handoff_tools=handoff_tools,
                    checkpointer=self.checkpointer,
                    api_key=getattr(state.extra_headers, 'api_key', None),
                    username=getattr(state.extra_headers, 'username', None)
                )
            agent.name = "mcp_agent"  # 设置名称用于映射
            return agent
            
        elif agent_type == "multimodal_agent":
            if use_react_mode:
                agent = await create_text_multimodal_agent_react(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer,
                    uploaded_files=state.file_uploads
                )
            else:
                agent = await create_text_multimodal_agent(
                    llm=llm,
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    checkpointer=self.checkpointer
                )
            agent.name = "multimodal_agent"  # 设置名称用于映射
            return agent
            
        elif agent_type == "custom_rag_agent":
            if use_react_mode:
                agent = await create_text_custom_rag_agent_react(
                    llm=llm,
                    custom_rag_ids=state.extral_rag_ids or [],
                    model_desc=state.model_desc,
                    handoff_tools=handoff_tools,
                    max_iterations=max_iterations,
                    enable_context_summarization=True,
                    checkpointer=self.checkpointer
                )
            agent.name = "custom_rag_agent"  # 设置名称用于映射
            return agent
    
        else:
            raise ValueError(f"未知的文本模式Agent类型: {agent_type}")
    
    def _create_llm(self, 
                    temperature: float = 0.6, 
                    extra_headers: Optional[Dict[str, Any]] = None,
                    model_name: str = "",
                    is_reasoning: bool = False,
                    agent_name: str = "unknown"):
        """创建LLM实例，自动注入token统计回调"""
        from langchain_deepseek import ChatDeepSeek
        from pydantic import SecretStr
        # 导入token统计功能
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from token_usage_tracker import create_token_callback
        
        if extra_headers is None:
            extra_headers = {}
        
        # 确定最终的模型名称    
        final_model_name = model_name if model_name else str(os.getenv("OPENAI_MODEL"))
        
        # 创建token统计回调
        token_callback = create_token_callback(agent_name, final_model_name)
        logger.info(f"为{agent_name}创建token回调，模型: {final_model_name}")
        
        # 准备回调列表
        callbacks = [token_callback]
            
        if not is_reasoning:
            llm = ChatOpenAI(
                model=final_model_name,
                api_key=SecretStr(str(os.getenv("OPENAI_API_KEY"))),
                base_url=str(os.getenv("OPENAI_BASE_URL")),
                temperature=temperature,
                top_p=0.9,
                default_headers=extra_headers,
                callbacks=callbacks,
                extra_body={"enable_thinking": False},
                stream_usage=True
            )
        else:
            llm = ChatDeepSeek(
                model=final_model_name,
                api_key=SecretStr(str(os.getenv("OPENAI_API_KEY"))),
                api_base=str(os.getenv("OPENAI_BASE_URL")),
                temperature=temperature,
                top_p=0.9,
                default_headers=extra_headers,
                callbacks=callbacks,
                extra_body={"enable_thinking": True},
                stream_usage=True
            )
        
        logger.info(f"LLM创建成功: {agent_name} -> {final_model_name}, callbacks: {len(callbacks)}")
        return llm
    
    def _get_agent_description(self, agent_type: str) -> str:
        """获取Agent的描述信息"""
        descriptions = {
            "simple_chat": "简单问答助手",
            "knowledge_agent": "三峡集团知识专家",
            "mcp_agent": "外部工具专家",
            "multimodal_agent": "多模态处理专家",
            "custom_rag_agent": "自定义知识库专家"
        }
        return descriptions.get(agent_type, agent_type)
    
    def _build_agent_config(self, state: Any, suffix: str = "") -> Dict[str, Any]:
        """构建Agent配置"""
        thread_id = state.thread_id or "default"
        if suffix:
            thread_id = f"{thread_id}_{suffix}"
        
        return {
            "configurable": {
                "thread_id": thread_id,
                "checkpoint_ns": "text_unified_multi_agent"
            }
        }