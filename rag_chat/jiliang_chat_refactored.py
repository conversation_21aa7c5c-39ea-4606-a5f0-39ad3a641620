import json
import os
import sys
import tempfile
import time
from datetime import datetime
from uuid import uuid4
from typing import Dict, Any, AsyncGenerator, Optional, Union
import threading
# 导入必需的模块
from datetime import datetime
import aiofiles
import aiofiles.os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.append(project_root)

import asyncio
import requests
from fastapi import APIRouter, Request, File, UploadFile, Form
from typing import List
from starlette.responses import StreamingResponse
from dotenv import load_dotenv
from pydantic import SecretStr
from fastapi.responses import JSONResponse

# LangChain imports
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState
from langgraph.checkpoint.memory import MemorySaver
try:
    from langgraph.checkpoint.redis.aio import AsyncRedisSaver
    ASYNC_REDIS_AVAILABLE = True
except ImportError:
    AsyncRedisSaver = None
    ASYNC_REDIS_AVAILABLE = False

# Local imports
from rag_chat.logger import logger
from rag_chat.models import ListMcpToolsRequest, MultiAgentQARequest, ThreadHistoryRequest, ThreadHistoryResponse
from rag_chat.constant import AI_ASSISTANT_HEADER, AI_ASSISTANT_WARN_URLENCODER
from rag_chat.redis_util import RedisClient
from rag_chat.multi_agent.text_parser import text_parser
from dm_util import DMDatabase
from rag_chat.context_summarizer import create_summarization_pre_model_hook, extend_state_for_summarization
from rag_chat.file_util import download_file
from rag_chat.models import UploadedFileInfo

# 工具
from rag_chat.jiliang_chat_tools import search_knowledge_base

load_dotenv(override=True)

# langfuse
use_langfuse = False
try:
    if use_langfuse:
        from langfuse import Langfuse
        from langfuse.langchain import CallbackHandler
        langfuse = Langfuse(
            public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
            secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
            host=os.getenv("LANGFUSE_HOST")
        )
        langfuse_handler = CallbackHandler()
    else:
        langfuse_handler = None
except:
    langfuse_handler = None

# MCP配置加载
def load_mcp_config():
    """从配置文件加载MCP服务配置"""
    config_path = os.path.join(os.path.dirname(__file__), 'jiliang_chat_mcp.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get('mcp_servers', {})
    except Exception as e:
        logger.error(f"加载MCP配置文件失败: {e}")
        return {}

# 加载MCP配置
MCP_SERVERS = load_mcp_config()

# MCP路由器配置
MCP_ROUTER_TYPE = os.getenv('MCP_ROUTER_TYPE', 'rag')  # 'rag' or 'simple'
MCP_API_BASE_URL = os.getenv('MCP_API_BASE_URL', 'http://localhost:8080')

# 初始化MCP路由器
from rag_chat.mcp_rag_router import MCPRAGRouter
from rag_chat.mcp_simple_router import MCPSimpleRouter

mcp_router = None
mcp_simple_router = None

async def initialize_mcp_router():
    """根据配置异步初始化MCP路由器"""
    global mcp_router, mcp_simple_router
    
    logger.info(f"初始化MCP路由器，类型: {MCP_ROUTER_TYPE}")
    
    try:
        if MCP_ROUTER_TYPE.lower() == 'rag':
            # 初始化RAG路由器
            config_path = os.path.join(os.path.dirname(__file__), 'jiliang_chat_mcp.json')
            milvus_host = os.getenv('MILVUS_HOST', '127.0.0.1')
            milvus_port = os.getenv('MILVUS_PORT', '19530')
            milvus_db = os.getenv('MILVUS_DB', 'jiliang_chat_mcp')
            milvus_user = os.getenv('MILVUS_USER', '')
            milvus_password = os.getenv('MILVUS_PASSWORD', '')
            
            mcp_router = MCPRAGRouter(
                config_path=config_path,
                milvus_host=milvus_host,
                milvus_port=milvus_port,
                milvus_db=milvus_db,
                milvus_user=milvus_user,
                milvus_password=milvus_password
            )
            
            # 异步初始化
            success = await mcp_router.initialize()
            if success:
                logger.info("MCP RAG路由器初始化成功")
            return success
            
        elif MCP_ROUTER_TYPE.lower() == 'simple':
            # 初始化简单路由器
            mcp_simple_router = MCPSimpleRouter(api_base_url=MCP_API_BASE_URL)
            logger.info("MCP Simple路由器初始化成功")
            return True
            
        else:
            logger.error(f"未知的MCP路由器类型: {MCP_ROUTER_TYPE}")
            return False
            
    except Exception as e:
        logger.error(f"MCP路由器初始化失败: {e}")
        return False

# Configuration
TEMP_BASE_DIR = os.getenv('TEMP_DIR', tempfile.gettempdir())
THRESHOLD = float(os.getenv('RAG_THRESHOLD', '0.8'))
rag_id = os.getenv('RAG_ID')
model = os.getenv('MODEL')
llm_base_url = os.getenv('LLM_BASE_URL')
rag_url = os.getenv('RAG_URL')
history_exp_time = int(os.getenv('HISTORY_EXP_TIME', '3600'))
mongo_collection = os.getenv('MONGO_COLLECTION_JILIANG')
redis_table = os.getenv('REDIS_TABLE_JILIANG')

# 文件下载缓存 - 避免重复下载相同ID的文件
_file_download_cache = {}  # {file_id: {"file_path": str, "last_access": datetime, "file_info": UploadedFileInfo}}
_cache_lock = threading.Lock()

# Qwen2.5-VL支持的图片格式常量
SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif']
SUPPORTED_IMAGE_MIME_TYPES = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.bmp': 'image/bmp',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff',
    '.gif': 'image/gif'
}

async def get_cached_file(file_id: int) -> Optional[Dict]:
    """获取缓存的文件信息"""
    import aiofiles.os
    
    # 先获取缓存信息，避免在锁内等待异步操作
    cached_info = None
    with _cache_lock:
        if file_id in _file_download_cache:
            cached_info = _file_download_cache[file_id].copy()
    
    if cached_info:
        # 异步检查文件是否仍然存在
        if await aiofiles.os.path.exists(cached_info["file_path"]):
            # 更新最后访问时间
            with _cache_lock:
                if file_id in _file_download_cache:  # 再次检查，防止并发删除
                    _file_download_cache[file_id]["last_access"] = datetime.now()
                    logger.info(f"使用缓存文件: ID={file_id}, 路径={cached_info['file_path']}")
                    return _file_download_cache[file_id]
        else:
            # 文件不存在，移除缓存条目
            with _cache_lock:
                if file_id in _file_download_cache:
                    del _file_download_cache[file_id]
                    logger.warning(f"缓存文件已不存在，移除缓存: ID={file_id}")
    
    return None

def cache_file(file_id: int, file_path: str, file_info) -> None:
    """缓存文件信息"""
    with _cache_lock:
        _file_download_cache[file_id] = {
            "file_path": file_path,
            "last_access": datetime.now(),
            "file_info": file_info
        }
        logger.info(f"文件已缓存: ID={file_id}, 路径={file_path}")

async def cleanup_expired_cache(max_age_hours: int = 2) -> None:
    """清理过期的缓存文件"""
    import aiofiles.os
    
    # 先获取过期文件列表，避免在锁内执行异步操作
    expired_items = []
    with _cache_lock:
        current_time = datetime.now()
        for file_id, cached_info in _file_download_cache.items():
            age = (current_time - cached_info["last_access"]).total_seconds() / 3600
            if age > max_age_hours:
                expired_items.append((file_id, cached_info["file_path"]))
    
    # 异步删除过期文件
    expired_keys = []
    for file_id, file_path in expired_items:
        try:
            if await aiofiles.os.path.exists(file_path):
                await aiofiles.os.remove(file_path)
                logger.info(f"删除过期缓存文件: ID={file_id}, 路径={file_path}")
            expired_keys.append(file_id)
        except Exception as e:
            logger.error(f"删除过期缓存文件失败: ID={file_id}, 错误={e}")
            expired_keys.append(file_id)  # 即使删除失败也从缓存中移除
    
    # 从缓存中移除已处理的过期条目
    if expired_keys:
        with _cache_lock:
            for key in expired_keys:
                if key in _file_download_cache:
                    del _file_download_cache[key]
        logger.info(f"清理了 {len(expired_keys)} 个过期缓存文件")

# Database configuration
dm_config_ck = {
    'host': os.getenv('DM_HOST', '127.0.0.1'),
    'port': int(os.getenv('DM_PORT', '5236')),
    'user': os.getenv('DM_USER', 'SYSDBA'),
    'password': os.getenv('DM_PASSWORD', '********'),
    'database': os.getenv('DM_DB', 'TEST_CHECKPOINTER'),
    'auto_commit': False
}

dm_config = {
    'host': os.getenv('DM_HOST'),
    'port': int(os.getenv('DM_PORT', '5236')),
    'user': os.getenv('DM_USER'),
    'password': os.getenv('DM_PASSWORD'),
    'database': os.getenv('DM_DB'),
    'auto_commit': False
}

# FastAPI应用启动事件处理器
async def startup_event_handler():
    """FastAPI应用启动时的初始化处理器"""
    logger.info("🚀 开始API服务启动初始化...")
    try:
        await global_service_initialization()
        logger.info("🎉 API服务启动初始化完成！")
    except Exception as e:
        logger.error(f"💥 API服务启动初始化失败: {e}")
        # 可以选择退出进程或者记录错误继续运行
        raise

def sync_shutdown():
    """Synchronous shutdown wrapper"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(cleanup_checkpointer())
    finally:
        loop.close()

router = APIRouter(on_startup=[startup_event_handler], on_shutdown=[sync_shutdown])

# 全局初始化状态标志
_global_initialization_completed = False
_global_initialization_lock = threading.Lock()

async def global_service_initialization():
    """
    全局服务初始化 - 在API服务启动时执行一次
    包括checkpointer和多智能体系统的初始化
    """
    global _global_initialization_completed, graph_checkpointer, mcp_router, mcp_simple_router
    
    with _global_initialization_lock:
        if _global_initialization_completed:
            logger.info("全局服务已初始化，跳过重复初始化")
            return
        
        logger.info("开始全局服务初始化...")
        start_time = time.time()
        
        try:
            # 1. 初始化checkpointer
            if not checkpointer_initialized:
                await initialize_checkpointer()
                logger.info("✅ Checkpointer初始化完成")
            
            # 2. 初始化MCP路由器
            if not mcp_router and not mcp_simple_router:
                success = await initialize_mcp_router()
                if success:
                    logger.info("✅ MCP路由器初始化完成")
                else:
                    logger.warning("⚠️ MCP路由器初始化失败，将影响工具调用功能")
            
            # 3. 初始化多智能体服务
            from rag_chat.multi_agent_service import get_multi_agent_service, initialize_multi_agent_service
            
            multi_agent_service = get_multi_agent_service()
            if not multi_agent_service.initialized:
                await initialize_multi_agent_service(
                    checkpointer=graph_checkpointer,
                    mcp_router=mcp_router,
                    mcp_simple_router=mcp_simple_router
                )
                logger.info("✅ 多智能体服务初始化完成")
            
            _global_initialization_completed = True
            initialization_time = time.time() - start_time
            logger.info(f"🚀 全局服务初始化完成！耗时: {initialization_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"❌ 全局服务初始化失败: {e}")
            raise

# Checkpointer for LangGraph - 添加线程锁和达梦数据库支持
_checkpointer_lock = threading.Lock()
graph_checkpointer: Optional[Any] = None
checkpointer_context: Optional[Any] = None
checkpointer_initialized = False

# Checkpointer 类型配置
CHECKPOINTER_TYPE = os.getenv('CHECKPOINTER_TYPE', 'dm')  # 'redis', 'dm', 'memory'

async def initialize_async_dm_checkpointer():
    """初始化异步达梦数据库 checkpointer"""
    global graph_checkpointer, checkpointer_context
    
    try:
        from checkpoint_dm import AsyncDMCheckpointSaver
        
        # 使用异步达梦数据库 checkpointer
        checkpointer_context = AsyncDMCheckpointSaver(dm_config_ck)
        graph_checkpointer = checkpointer_context
        
        # 异步初始化数据库表
        try:
            await graph_checkpointer.asetup()
            logger.info("异步达梦数据库 checkpointer 设置完成")
        except Exception as setup_error:
            logger.warning(f"异步达梦数据库 checkpointer 设置失败: {setup_error}")
        
        logger.info("使用异步达梦数据库 checkpointer")
        return True
        
    except Exception as e:
        logger.warning(f"异步达梦数据库 checkpointer 初始化失败: {e}")
        return False

async def initialize_async_redis_checkpointer():
    """初始化异步Redis checkpointer"""
    global graph_checkpointer, checkpointer_context
    
    if not ASYNC_REDIS_AVAILABLE or AsyncRedisSaver is None:
        logger.warning("AsyncRedisSaver不可用，使用内存checkpointer")
        return False
    
    try:
        # 构建Redis连接字符串
        redis_url = build_redis_url()
        
        # 使用AsyncRedisSaver上下文管理器
        checkpointer_context = AsyncRedisSaver.from_conn_string(redis_url)
        graph_checkpointer = await checkpointer_context.__aenter__()
        
        # 异步初始化索引
        try:
            await graph_checkpointer.asetup()
            logger.info("异步Redis索引设置完成")
        except Exception as setup_error:
            logger.warning(f"异步Redis索引设置失败: {setup_error}")
        
        logger.info(f"使用异步Redis checkpointer: {redis_url}")
        return True
        
    except Exception as e:
        logger.warning(f"异步Redis checkpointer初始化失败: {e}")
        return False


def build_redis_url():
    """构建Redis连接字符串"""
    redis_client = RedisClient()
    # 检查redis_client的属性
    if hasattr(redis_client, 'hosts_str') and redis_client.hosts_str:
        # 从hosts_str中解析第一个主机
        hosts = redis_client.hosts_str.split(',')
        if hosts:
            host_port = hosts[0].strip()
            if ':' in host_port:
                host, port = host_port.split(':')
                redis_url = f"redis://{host}:{port}/{redis_client.db}"
            else:
                redis_url = f"redis://{host_port}:6379/{redis_client.db}"
        else:
            # 使用默认Redis配置
            redis_url = "redis://localhost:6379/0"
    else:
        # 使用默认Redis配置
        redis_url = "redis://localhost:6379/0"
    
    # 添加密码支持
    if hasattr(redis_client, 'password') and redis_client.password:
        redis_url = redis_url.replace('redis://', f'redis://:{redis_client.password}@')
    
    return redis_url

# 临时使用内存checkpointer，在应用启动时替换
graph_checkpointer = MemorySaver()

async def initialize_checkpointer():
    """根据配置初始化合适的 checkpointer"""
    global graph_checkpointer, checkpointer_initialized
    
    with _checkpointer_lock:
        if checkpointer_initialized:
            return
    
    logger.info(f"开始初始化 {CHECKPOINTER_TYPE} checkpointer...")
    
    try:
        success = False
        
        if CHECKPOINTER_TYPE.lower() == 'dm':
            success = await initialize_async_dm_checkpointer()
        elif CHECKPOINTER_TYPE.lower() == 'redis':
            success = await initialize_async_redis_checkpointer()
        elif CHECKPOINTER_TYPE.lower() == 'memory':
            success = True
            graph_checkpointer = MemorySaver()
            logger.info("使用内存 checkpointer")
        else:
            logger.warning(f"未知的 checkpointer 类型: {CHECKPOINTER_TYPE}，回退到达梦数据库")
            success = await initialize_async_dm_checkpointer()
        
        with _checkpointer_lock:
            if success:
                checkpointer_initialized = True
                logger.info(f"{CHECKPOINTER_TYPE} checkpointer初始化成功")
            else:
                logger.warning(f"{CHECKPOINTER_TYPE} checkpointer初始化失败，使用内存checkpointer")
                graph_checkpointer = MemorySaver()
                checkpointer_initialized = True
            
    except Exception as e:
        logger.error(f"Checkpointer初始化异常: {e}")
        with _checkpointer_lock:
            graph_checkpointer = MemorySaver()
            checkpointer_initialized = True

def query_model_description(model_name: str) -> Optional[str]:
    """根据模型名称查询描述信息"""
    try:
        with DMDatabase(**dm_config) as db:
            query_sql = """
                SELECT DESCRIPTION
                FROM "SX_AIPLATFORM"."T_BASE_LLM_MODEL"
                WHERE "MODEL_NAME" = :1
            """
            result = db.execute_query(query_sql, (model_name,))
            if result and len(result) > 0:
                desc = result[0]['DESCRIPTION']
                logger.info(f"模型描述：{desc}")
                return desc
            else:
                logger.info(f"未找到模型 {model_name} 的描述信息")
                return None
    except Exception as e:
        logger.info(f"查询模型信息失败: {e}")
        return None

def query_model_info(model_name: str) -> Optional[Dict]:
    """根据模型名称查询模型信息"""
    try:
        with DMDatabase(**dm_config) as db:
            query_sql = """
                SELECT *
                FROM "SX_AIPLATFORM"."T_BASE_LLM_MODEL"
                WHERE "MODEL_NAME" = :1
            """
            result = db.execute_query(query_sql, (model_name,))
            if result and len(result) > 0:
                model_info = result[0]
                logger.info(f"模型信息：{model_info}")
                return model_info
            else:
                logger.info(f"未找到模型 {model_name} 的信息")
                return None
    except Exception as e:
        logger.info(f"查询模型信息失败: {e}")
        return None

def query_user_model(user_name: str) -> Optional[str]:
    """根据用户名查询保存的模型名"""
    try:
        with DMDatabase(**dm_config) as db:
            query_sql = """
                SELECT MODEL_NAME
                FROM "SX_AIPLATFORM"."T_USER_MODEL_PREFERENCE"
                WHERE "USER_NAME" = :1
            """
            result = db.execute_query(query_sql, (user_name,))
            if result and len(result) > 0:
                model_name = result[0]['MODEL_NAME']
                logger.info(f"用户模型：{model_name}")
                return model_name
            else:
                logger.info(f"未找到用户 {user_name} 的模型偏好")
                return None
    except Exception as e:
        logger.info(f"查询模型信息失败: {e}")
        return None

def get_sys_prompt(model_name: str) -> str:
    """获取系统提示词"""
    from jiliang_chat_prompt import DEFAULT_PROMPT
    model_desc = query_model_description(model_name)
    return DEFAULT_PROMPT.format(model_desc=(model_desc if model_desc else "DeepSeek"))

def save_record_dm(question, answer, reasoning, chat_type, userid='', apikey='', qa_id=''):
    """Save conversation record to database"""
    try:
        with DMDatabase(**dm_config) as db:
            try:
                insert_count = db.execute_update(
                    "INSERT INTO SX_AIPLATFORM.sx_jiliang_records (QUESTION, ANSWER, REASONING, CHAT_TYPE, CREATETIME, USERID, APIKEY, QA_ID) VALUES (:1, :2, :3, :4, :5, :6, :7, :8)",
                    (question, answer, reasoning, chat_type, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), userid, apikey, qa_id)
                )
                logger.info(f"插入行数: {insert_count}")
                db.commit()
            except Exception as e:
                logger.error(f"数据库操作异常: {str(e)}", exc_info=True)
                if 'db' in locals():
                    db.rollback()
    except Exception as e:
        logger.error(e)

# Create the LangGraph ReAct agent with dynamic MCP tools
async def create_dynamic_agent(model_name: str,
                              question: str,
                              temperature: float = 0.6, 
                              top_p: float = 0.9, 
                              extra_headers: dict = {},
                              mcp_ids: list = []):
    """Create a LangGraph ReAct agent with dynamically selected MCP tools"""
    global mcp_router, mcp_simple_router
    
    # 确保MCP路由器已初始化
    if MCP_ROUTER_TYPE.lower() == 'rag' and mcp_router is None:
        logger.info("初始化MCP RAG路由器...")
        if not await initialize_mcp_router():
            logger.warning("MCP RAG路由器初始化失败，使用基础agent")
            return create_basic_agent(model_name, temperature, top_p, extra_headers)
    elif MCP_ROUTER_TYPE.lower() == 'simple' and mcp_simple_router is None:
        logger.info("初始化MCP Simple路由器...")
        if not await initialize_mcp_router():
            logger.warning("MCP Simple路由器初始化失败，使用基础agent")
            return create_basic_agent(model_name, temperature, top_p, extra_headers)
    
    # Create model instance with specified parameters
    agent_llm = ChatOpenAI(
        model=str(os.getenv("OPENAI_MODEL")),
        api_key=SecretStr(str(os.getenv("OPENAI_API_KEY"))),
        base_url=str(os.getenv("OPENAI_BASE_URL")),
        temperature=temperature,
        top_p=top_p,
        default_headers=extra_headers
    )
    
    # Get system prompt for the model
    system_prompt = get_sys_prompt(model_name)
    
    # Create context summarization pre_model_hook function
    pre_model_hook = create_summarization_pre_model_hook()
    
    # Create extended state schema for summarization
    @extend_state_for_summarization
    class ExtendedAgentState(AgentState):
        pass
    
    # 根据路由器类型动态获取MCP工具
    mcp_tools = []
    try:
        if MCP_ROUTER_TYPE.lower() == 'rag' and mcp_router:
            mcp_tools = await mcp_router.get_dynamic_mcp_tools(question, top_k=2)
            logger.info(f"RAG路由器为问题 '{question[:50]}...' 动态加载了 {len(mcp_tools)} 个MCP工具")
        elif MCP_ROUTER_TYPE.lower() == 'simple' and mcp_simple_router:
            # 对于简单路由器，需要先为用户初始化
            if mcp_ids:
                success = await mcp_simple_router.initialize_for_user(mcp_ids)
                if success:
                    mcp_tools = await mcp_simple_router.get_dynamic_mcp_tools(question)
                    logger.info(f"Simple路由器为 {mcp_ids} 加载了 {len(mcp_tools)} 个MCP工具")
                else:
                    logger.warning(f"Simple路由器为 {mcp_ids} 初始化失败")
            else:
                logger.warning("Simple路由器需要user_id，但未提供")
    except Exception as e:
        logger.error(f"动态加载MCP工具失败: {e}")
        mcp_tools = []
    
    # Create ReAct agent with knowledge base search tool, dynamic MCP tools, and context summarization
    agent = create_react_agent(
        model=agent_llm,
        tools=[search_knowledge_base, *mcp_tools],
        prompt=system_prompt,  # 使用原始系统提示词
        pre_model_hook=pre_model_hook,  # 通过pre_model_hook进行上下文总结
        state_schema=ExtendedAgentState,  # 使用扩展的状态模式支持running_summary字段
        checkpointer=graph_checkpointer
    )
    
    return agent

def create_basic_agent(model_name: str, 
                      temperature: float = 0.6, 
                      top_p: float = 0.9, 
                      extra_headers: dict = {}):
    """Create a basic LangGraph ReAct agent without MCP tools (fallback)"""
    # Create model instance with specified parameters
    agent_llm = ChatOpenAI(
        model=str(os.getenv("OPENAI_MODEL")),
        api_key=SecretStr(str(os.getenv("OPENAI_API_KEY"))),
        base_url=str(os.getenv("OPENAI_BASE_URL")),
        temperature=temperature,
        top_p=top_p,
        default_headers=extra_headers
    )
    
    # Get system prompt for the model
    system_prompt = get_sys_prompt(model_name)
    
    # Create context summarization pre_model_hook function
    pre_model_hook = create_summarization_pre_model_hook()
    
    # Create extended state schema for summarization
    @extend_state_for_summarization
    class ExtendedAgentState(AgentState):
        pass
    
    # Create ReAct agent with only knowledge base search tool and context summarization
    agent = create_react_agent(
        model=agent_llm,
        tools=[search_knowledge_base],
        prompt=system_prompt,  # 使用原始系统提示词
        pre_model_hook=pre_model_hook,  # 通过pre_model_hook进行上下文总结
        state_schema=ExtendedAgentState,  # 使用扩展的状态模式支持running_summary字段
        checkpointer=graph_checkpointer
    )
    
    return agent

async def stream_agent_response(agent, message: str, config: Dict[str, Any], userid: str = '', apikey: str = '', qa_id: str = '') -> AsyncGenerator[str, None]:
    """Stream the agent's response"""
    full_response = ""
    reasoning_content = ""
    tool_content = ""
    
    try:
        # Create messages for the agent
        messages = [HumanMessage(content=message)]
        
        # 使用token级别的异步流式响应
        logger.debug("使用token级别异步流式响应")
        
        async for message_chunk, metadata in agent.astream(
            {"messages": messages},
            config=config,
            stream_mode="messages"
        ):
            if hasattr(message_chunk, 'content'):
                if message_chunk.content:
                    # 只处理AI消息的token，忽略工具消息等
                    if isinstance(message_chunk, AIMessage):
                        token_content = message_chunk.content
                        full_response += token_content
                        
                        # Format as SSE for compatibility with original interface
                        data = {
                            'type': 'text',
                            'data': token_content
                        }
                        yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                    elif isinstance(message_chunk, ToolMessage):
                        token_tool_content = message_chunk.content
                        tool_content += token_tool_content
                        data = {
                            'type': 'tool',
                            'data': token_tool_content
                        }
                        yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                elif message_chunk.additional_kwargs.get('reasoning_content'):
                    token_reasoning_content = message_chunk.additional_kwargs.get('reasoning_content')
                    reasoning_content += token_reasoning_content
                    data = {
                        'type': 'reasoning_text',
                        'data': token_reasoning_content
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                else:
                    continue
            else:
                continue
    
    except Exception as e:
        logger.error(f"Agent streaming error: {e}")
        error_data = {
            'type': 'error',
            'data': str(e)
        }
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

@router.post("/jiliang/chat/ListMcpTools")
async def list_mcp_tools(request: Request, body: ListMcpToolsRequest):
    """
    List MCP tools and upload tool schemas
    """
    logger.info(f"MCP IDs: {body.mcp_ids}，开始尝试加载MCP工具")
    mcp_test = MCPSimpleRouter(api_base_url=MCP_API_BASE_URL)
    
    # Get URLs from environment variables
    tools_upload_url = os.getenv('MCP_API_BASE_URL', '').rstrip('/') + '/ai/mcpServer/addTool'
    status_update_url = os.getenv('MCP_API_BASE_URL', '').rstrip('/') + '/ai/mcpServer/updateStatus'
    
    if body.mcp_ids:
        success = await mcp_test.initialize_for_user(body.mcp_ids)
        if success:
            mcp_tools = await mcp_test.get_dynamic_mcp_tools()
            logger.info(f"MCP路由器为 {body.mcp_ids} 加载了 {len(mcp_tools)} 个MCP工具")
            
            # Extract tool names and parameters for each mcp_id
            tools_data = []
            for mcp_id in body.mcp_ids:
                for tool in mcp_tools:
                    tool_info = {
                        "serverId": str(mcp_id),
                        "toolName": tool.name,
                        "toolDesc": tool.description,
                        "toolArgs": json.dumps(tool.args_schema) if tool.args_schema else "{}"
                    }
                    tools_data.append(tool_info)
            
            # Upload tools schema
            try:
                upload_response = requests.post(
                    tools_upload_url,
                    json=tools_data,
                    headers={"Content-Type": "application/json",
                             "x-api-key": os.getenv("MCP-X-API-Key"),
                             "x-username": os.getenv("MCP-X-Username")
                             },
                    timeout=30
                )
                logger.info(f"工具上传响应: {upload_response.status_code}")
            except Exception as e:
                logger.error(f"工具上传失败: {e}")
            
            # Update status to success (1) for each mcp_id
            for mcp_id in body.mcp_ids:
                try:
                    status_response = requests.post(
                        status_update_url,
                        json={"id": str(mcp_id), "status": 1},
                        headers={"Content-Type": "application/json",
                                 "x-api-key": os.getenv("MCP-X-API-Key"),
                                 "x-username": os.getenv("MCP-X-Username")
                                 },
                        timeout=30
                    )
                    logger.info(f"MCP ID {mcp_id} 状态更新响应: {status_response.status_code}")
                except Exception as e:
                    logger.error(f"MCP ID {mcp_id} 状态更新失败: {e}")
            
            return {"success": True, "message": "MCP工具加载成功", "tools_count": len(mcp_tools)}
        else:
            logger.warning(f"MCP路由器为 {body.mcp_ids} 初始化失败")
            # Update status to failure (0) for each mcp_id
            for mcp_id in body.mcp_ids:
                try:
                    status_response = requests.post(
                        status_update_url,
                        json={"id": str(mcp_id), "status": 0},
                        headers={"Content-Type": "application/json",
                                 "x-api-key": os.getenv("MCP-X-API-Key"),
                                 "x-username": os.getenv("MCP-X-Username")
                                 },
                        timeout=30
                    )
                    logger.info(f"MCP ID {mcp_id} 状态更新响应: {status_response.status_code}")
                except Exception as e:
                    logger.error(f"MCP ID {mcp_id} 状态更新失败: {e}")
            
            return {"success": False, "message": "MCP工具加载失败，未能成功连接"}
    else:
        logger.warning("MCP路由器需要mcp_ids，但未提供")
        return {"success": False, "message": "未提供mcp_ids"}

@router.post("/jiliang/chat/v2")
async def chat_stream_v2(request: Request, body: MultiAgentQARequest):
    """
    Refactored chat interface using LangChain and LangGraph
    """
    # 确保 checkpointer 已初始化
    if not checkpointer_initialized:
        await initialize_checkpointer()
    
    header = dict(request.headers)
    apikey = header.get('x-api-key', '')
    userid = header.get('x-username', '')
    uri = header.get('x-uri', request.url.path)
    
    logger.info(f"Header: {header}")
    logger.info(f"RequestURI: {uri}")
    logger.info(f"Username: {userid}")
    logger.info(f"Api-Key: {apikey}")
    logger.info(f"QaID: {body.qa_id}")
    logger.info(f"Model: {body.model}")
    logger.info(f"McpIds: {body.mcp_ids}")
    logger.info(f"ExtralRagIds: {body.extral_rag_ids}")

    # Generate unique session ID if not provided
    qa_id = body.qa_id
    if qa_id is None or qa_id == '':
        qa_id = str(uuid4())

    # Determine model to use
    model_name = body.model
    if model_name is None or model_name == '':
        if userid is not None and userid != '':
            user_model = query_user_model(userid)
            if user_model is not None and user_model != '':
                model_name = user_model
    
    # 确保model_name不为空，使用默认模型
    if model_name is None or model_name == '':
        model_name = model if model else 'deepseek-chat'

    # Get temperature parameter
    temperature = body.temperature if hasattr(body, 'temperature') and body.temperature is not None else 0.6

    # Create dynamic LangGraph agent
    extra_headers = {'Qa-Id': qa_id, 'X-API-Key': apikey, 'X-Username': userid, 'X-Uri': uri}
    agent = await create_dynamic_agent(model_name,
                                      body.question,
                                      temperature, 
                                      top_p=0.9, 
                                      extra_headers=extra_headers,
                                      mcp_ids=list(body.mcp_ids) if body.mcp_ids else [])
    
    # Configuration for the agent (thread management)
    config = {
        "configurable": {"thread_id": f"{userid}--{qa_id}", "checkpoint_ns": "chat_stream_v2"}
    }
    
    # Response headers
    headers = {
        "Qa-Id": qa_id,
        AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
    }
    
    # Stream the agent's response
    return StreamingResponse(
        stream_agent_response(agent, body.question, config, userid, apikey, qa_id),
        media_type="text/event-stream; charset=utf-8",
        headers=headers
    )


@router.post("/jiliang/chat/v3")
async def chat_stream_v3(request: Request, body: MultiAgentQARequest):
    """
    多智能体聊天接口 - 使用统一的LangGraph系统，宇宙级多智能体服务
    """

    # 确保全局服务已初始化（快速检查，不进行初始化）
    if not _global_initialization_completed:
        logger.error("全局服务未初始化，请检查服务启动流程")
        return JSONResponse(
            content={"error": "服务未完全启动，请稍后重试"},
            status_code=503
        )
    
    # 获取已初始化的多智能体服务
    from rag_chat.multi_agent_service import get_multi_agent_service
    multi_agent_service = get_multi_agent_service()
    
    header = dict(request.headers)
    apikey = header.get('x-api-key', '')
    userid = header.get('x-username', '')
    uri = header.get('x-uri', request.url.path)
    
    logger.info(f"V3 API - Header: {header}")
    logger.info(f"V3 API - RequestURI: {uri}")
    logger.info(f"V3 API - Username: {userid}")
    logger.info(f"V3 API - Api-Key: {apikey}")
    logger.info(f"V3 API - QaID: {body.qa_id}")
    logger.info(f"V3 API - Model: {body.model}")
    logger.info(f"V3 API - McpIds: {body.mcp_ids}")
    logger.info(f"V3 API - ExtralRagIds: {body.extral_rag_ids}")

    # Generate unique session ID if not provided
    qa_id = body.qa_id
    if qa_id is None or qa_id == '':
        qa_id = str(uuid4())

    # Determine model to use
    model_name = body.model
    if model_name is None or model_name == '':
        if userid is not None and userid != '':
            user_model = query_user_model(userid)
            if user_model is not None and user_model != '':
                model_name = user_model
    
    # 确保model_name不为空，使用默认模型
    if model_name is None or model_name == '':
        model_name = model if model else 'deepseek-chat'

    # 构建额外请求头
    extra_headers = {
        'Qa-Id': qa_id, 
        'X-API-Key': apikey, 
        'X-Username': userid, 
        'X-Uri': uri
    }
    
    # 更新请求模型信息
    body.model = model_name
    
    # 处理文件ID下载逻辑（替代原有的文件上传逻辑）
    uploaded_files_info = []
    file_cleanup_tasks = []
    
    # 获取临时目录
    temp_dir = os.getenv("TEMP_DIR", "/tmp")
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # 合并文件IDs和图片IDs
        all_file_ids = []
        if body.files_ids:
            all_file_ids.extend([(fid, False) for fid in body.files_ids])  # (file_id, is_image)
        if body.images_ids:
            all_file_ids.extend([(iid, True) for iid in body.images_ids])  # (file_id, is_image)
        
        logger.info(f"V3 API - 开始处理文件下载，总计 {len(all_file_ids)} 个文件")
        
        # 构建下载请求头
        download_headers = {
            'x-api-key': apikey,
            'x-username': userid
        }
        
        # 先清理过期缓存
        await cleanup_expired_cache(max_age_hours=2)
        
        # 处理所有文件（优先使用缓存）
        for file_id, is_image_hint in all_file_ids:
            try:
                # 首先检查缓存
                cached_file = await get_cached_file(file_id)
                if cached_file:
                    # 使用缓存文件
                    file_info = cached_file["file_info"]
                    uploaded_files_info.append(file_info)
                    logger.info(f"使用缓存文件: ID={file_id}, 文件名={file_info.filename}")
                    continue
                
                logger.info(f"缓存未命中，开始从OBS下载文件ID: {file_id}")
                
                # 调用file_util的下载方法
                downloaded_file_path = await download_file(file_id, headers=download_headers)
                
                # 异步获取文件信息
                import aiofiles.os as aios
                file_stat = await aios.stat(downloaded_file_path)
                file_size = file_stat.st_size
                original_filename = os.path.basename(downloaded_file_path)
                
                # 重新命名文件，避免冲突
                file_extension = os.path.splitext(original_filename)[1]
                safe_filename = f"{qa_id}_{file_id}{file_extension}"
                new_file_path = os.path.join(temp_dir, safe_filename)
                
                # 移动文件到统一的temp目录
                if downloaded_file_path != new_file_path:
                    await aiofiles.os.rename(downloaded_file_path, new_file_path)
                
                # 基于文件扩展名判断文件类型（Qwen2.5-VL支持的图片格式）
                is_image = file_extension.lower() in SUPPORTED_IMAGE_EXTENSIONS
                
                # 如果有明确的图片提示，优先使用提示
                if is_image_hint:
                    is_image = True
                
                # 确定content_type（Qwen2.5-VL支持的图片格式）
                if is_image:
                    content_type = SUPPORTED_IMAGE_MIME_TYPES.get(file_extension.lower(), 'image/jpeg')
                else:
                    content_type = "application/octet-stream"
                
                # 创建文件信息对象
                file_info = UploadedFileInfo(
                    file_path=new_file_path,
                    filename=original_filename,
                    file_size=file_size,
                    content_type=content_type,
                    is_image=is_image,
                    upload_time=datetime.now().isoformat()
                )
                uploaded_files_info.append(file_info)
                
                # 缓存文件信息
                cache_file(file_id, new_file_path, file_info)
                
                logger.info(f"文件下载成功: ID={file_id}, 文件名={original_filename}, 大小={file_size}, 类型={'图片' if is_image else '文档'}")
                
                # 创建文件清理任务（2小时后删除，与缓存清理时间一致）
                async def cleanup_file(file_path_to_cleanup, file_id_to_cleanup):
                    await asyncio.sleep(7200)  # 2小时后
                    try:
                        if await aiofiles.os.path.exists(file_path_to_cleanup):
                            await aiofiles.os.remove(file_path_to_cleanup)
                            logger.info(f"已清理过期文件: {file_path_to_cleanup}")
                            # 同时清理缓存
                            with _cache_lock:
                                if file_id_to_cleanup in _file_download_cache:
                                    del _file_download_cache[file_id_to_cleanup]
                    except Exception as e:
                        logger.error(f"清理文件失败 {file_path_to_cleanup}: {e}")
                
                file_cleanup_tasks.append(asyncio.create_task(cleanup_file(new_file_path, file_id)))
                
            except Exception as file_error:
                logger.error(f"下载文件ID {file_id} 失败: {file_error}")
                # 继续处理其他文件，不中断整个流程
                continue
        
        # 将文件信息添加到body中
        body.uploaded_files = uploaded_files_info
        
        if uploaded_files_info:
            logger.info(f"V3 API - 成功下载并处理 {len(uploaded_files_info)} 个文件")
        else:
            logger.warning("V3 API - 未成功下载任何文件")
                
    except Exception as e:
        logger.error(f"V3 API - 文件下载处理失败: {e}")
        # 异步清理已下载的文件
        for file_info in uploaded_files_info:
            try:
                if await aiofiles.os.path.exists(file_info.file_path):
                    await aiofiles.os.remove(file_info.file_path)
            except:
                pass
        return JSONResponse(
            content={"error": f"文件下载处理失败: {str(e)}"},
            status_code=500
        )
    
    # Response headers
    headers = {
        "Qa-Id": qa_id,
        "Agent-System": "MultiAgent-v3",
        AI_ASSISTANT_HEADER: AI_ASSISTANT_WARN_URLENCODER
    }
    
    # 最终检查服务状态
    logger.info(f"V3 API - 即将调用process_question，服务状态: initialized={multi_agent_service.initialized}")
    
    # 使用多智能体服务处理请求
    return StreamingResponse(
        multi_agent_service.process_question(
            request=body,
            userid=userid,
            apikey=apikey,
            qa_id=qa_id,
            extra_headers=extra_headers,
            show_thinking=True,
            execution_mode="react_mode",
            langfuse_handler=langfuse_handler
        ),
        media_type="text/event-stream; charset=utf-8",
        headers=headers
    )

async def cleanup_checkpointer():
    """清理 checkpointer 连接"""
    global checkpointer_context, graph_checkpointer
    
    if checkpointer_context is not None:
        try:
            # 处理异步上下文管理器
            if hasattr(checkpointer_context, '__aexit__'):
                await checkpointer_context.__aexit__(None, None, None)
                logger.info("异步 checkpointer 连接已关闭")
            elif hasattr(checkpointer_context, 'aclose'):
                # 达梦数据库 checkpointer 的清理方法
                await checkpointer_context.aclose()
                logger.info("达梦数据库 checkpointer 连接已关闭")
            else:
                logger.warning("checkpointer 不需要清理")
        except Exception as e:
            logger.warning(f"关闭 checkpointer 连接时出错: {e}")

async def cleanup_redis_checkpointer():
    """清理异步Redis checkpointer连接（已弃用，保持兼容性）"""
    await cleanup_checkpointer()

def _convert_langgraph_message_to_display_format(msg, state_snapshot):
    """
    将LangChain消息转换为显示格式
    与multi_agent_service中的消息处理逻辑保持完全一致
    """
    # 基础快照信息
    base_info = {
        "checkpoint_id": state_snapshot.config.get("configurable", {}).get("checkpoint_id"),
        "step": state_snapshot.metadata.get("step") if state_snapshot.metadata else None,
        "timestamp": getattr(msg, 'timestamp', None),
        "created_at": state_snapshot.created_at if hasattr(state_snapshot, 'created_at') else None
    }
    
    if hasattr(msg, 'type') and hasattr(msg, 'content'):
        message_type = msg.type

        if type(msg.content) == list:
            content = " ".join([item.get('text', '') for item in msg.content if isinstance(item, dict) and item.get('type') == 'text'])
        else:
            content = str(msg.content) if msg.content else ""
        
        # 基础消息字典
        message_dict = {
            "type": message_type,
            "content": content,
            "id": getattr(msg, 'id', None),
            **base_info
        }
        
        # 处理不同类型的消息
        if message_type == "human":
            # 检查是否是兼容性模式下的工具结果消息
            is_tool_result = (hasattr(msg, 'additional_kwargs') and 
                             msg.additional_kwargs and 
                             msg.additional_kwargs.get('is_tool_result', False))
            
            if is_tool_result:
                # 兼容性模式下的工具结果消息 (ENABLE_TOOLMESSAGE=false)
                tool_info = msg.additional_kwargs.get('tool_message_info', {})
                
                # 过滤掉Agent状态报告消息和handoff工具消息
                if tool_info.get('type') == 'agent_status_report':
                    return None  # 跳过这类消息
                
                tool_name = tool_info.get('tool_name', 'unknown_tool')
                
                # 过滤掉handoff和transfer相关的工具消息
                if tool_name and any(keyword in tool_name.lower() for keyword in 
                                   ['handoff_to', 'transfer_to', 'handoff', 'swarm_handoff']):
                    return None  # 跳过handoff相关的工具消息
                display_name = tool_info.get('display_name', tool_name)
                tool_call_id = tool_info.get('tool_call_id', '')
                original_content = tool_info.get('content', content)
                
                # 截断长内容用于显示
                display_content = original_content[:500] + '...' if len(original_content) > 500 else original_content
                
                message_dict.update({
                    "role": "tool",
                    "display_type": "tool_result",
                    "tool_name": tool_name,
                    "tool_call_id": tool_call_id,
                    "display_name": display_name,
                    "formatted_content": display_content,
                    "full_content": original_content,
                    "name": tool_name
                })
            else:
                # 真正的人类消息 - 对应HumanMessage
                message_dict.update({
                    "role": "human",
                    "display_type": "human_message",
                    "formatted_content": content
                })
                
                # 检查是否包含文件信息，将所有additional_kwargs传递给前端处理
                if hasattr(msg, 'additional_kwargs') and msg.additional_kwargs:
                    # 直接将所有additional_kwargs添加到消息字典中
                    for key, value in msg.additional_kwargs.items():
                        message_dict[key] = value
            
        elif message_type == "ai":
            # AI消息 - 对应AIMessage，需要解析结构化内容
            agent_name = "assistant"
            
            # 过滤掉Swarm模式的内部转交消息
            if content and any(pattern in content for pattern in [
                '【', '的处理结果】', '【转交给当前Agent的任务】',
                '请基于以上', '的处理结果和转交任务，继续完成用户的需求'
            ]):
                return None
            
            # 提取agent信息 (与service中的逻辑一致)
            if hasattr(msg, 'name') and msg.name:
                agent_name = msg.name
                message_dict["agent"] = msg.name
                message_dict["name"] = msg.name
            elif hasattr(msg, 'additional_kwargs') and msg.additional_kwargs:
                if 'agent' in msg.additional_kwargs:
                    agent_name = msg.additional_kwargs['agent']
                    message_dict["agent"] = agent_name
                message_dict["additional_kwargs"] = msg.additional_kwargs
            
            # 转换友好的agent名称
            friendly_agent_names = {
                "knowledge_agent": "知识专家", "simple_chat": "通用助手", "mcp_agent": "工具专家",
                "multimodal_agent": "多模态专家", "custom_rag_agent": "自定义知识库专家",
                "intent_analyzer": "意图分析器", "system_context_summarizer": "上下文总结器"
            }
            message_dict["agent_display_name"] = friendly_agent_names.get(agent_name, agent_name)
            
            # 解析AI消息中的结构化内容
            parsed_content, formatted_content = _parse_ai_message_content(content)
            message_dict.update({
                "role": "assistant",
                "display_type": "ai_message", 
                "formatted_content": formatted_content,
                "parsed_elements": parsed_content,
                "agent": agent_name
            })
            
            # 处理工具调用信息
            tool_calls = []
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                for tool_call in msg.tool_calls:
                    tool_calls.append({
                        "id": getattr(tool_call, 'id', ''),
                        "name": getattr(tool_call, 'name', ''),
                        "args": getattr(tool_call, 'args', {})
                    })
            elif hasattr(msg, 'additional_kwargs') and 'tool_calls' in msg.additional_kwargs:
                tool_calls = msg.additional_kwargs['tool_calls']
                
            if tool_calls:
                message_dict["tool_calls"] = tool_calls
            
        elif message_type == "tool":
            # 工具消息 - 对应ToolMessage
            tool_name = getattr(msg, 'name', 'unknown_tool')
            tool_call_id = getattr(msg, 'tool_call_id', '')
            
            # 过滤掉handoff和transfer相关的工具消息
            if tool_name and any(keyword in tool_name.lower() for keyword in 
                               ['handoff_to', 'transfer_to', 'handoff', 'swarm_handoff']):
                return None  # 跳过handoff相关的工具消息
            
            # 截断长内容用于显示
            display_content = content[:500] + '...' if len(content) > 500 else content
            
            message_dict.update({
                "role": "tool",
                "display_type": "tool_result",
                "tool_name": tool_name,
                "tool_call_id": tool_call_id,
                "display_name": tool_name,
                "formatted_content": display_content,
                "full_content": content,
                "name": tool_name})
            
        else:
            # 其他未知类型
            message_dict.update({
                "role": "unknown",
                "display_type": "unknown_message", 
                "formatted_content": content
            })
        
        return message_dict
        
    elif isinstance(msg, dict):
        # 字典格式的消息
        return {
            **msg,
            **base_info,
            "display_type": msg.get("type", "unknown"),
            "formatted_content": msg.get("content", str(msg))
        }
        
    else:
        # 其他格式，转换为字符串
        return {
            "type": "unknown",
            "content": str(msg),
            "role": "unknown",
            "display_type": "unknown_message",
            "formatted_content": str(msg),
            **base_info
        }

def _parse_ai_message_content(content):
    """
    解析AI消息内容中的结构化标记
    与multi_agent_service中的_detect_text_message_type逻辑一致
    """
    import re
    import json
    
    parsed_elements = []
    formatted_content = content
    
    # 检测和解析各种结构化标记
    
    # 1. 思考内容 <think>...</think>
    think_pattern = r'<think>(.*?)</think>'
    for match in re.finditer(think_pattern, content, re.DOTALL):
        thinking_content = match.group(1).strip()
        if thinking_content:
            parsed_elements.append({
                "type": "reasoning",
                "content": thinking_content,
                "start": match.start(),
                "end": match.end()
            })
        # 从显示内容中移除思考标记
        formatted_content = formatted_content.replace(match.group(0), "")
    
    # 2. 工具调用 [TOOL_CALL]...[/TOOL_CALL] 或 <tool_call>...</tool_call>
    tool_patterns = [
        r'\[TOOL_CALL\]\s*(.*?)\s*\[/TOOL_CALL\]',
        r'<tool_call>\s*(.*?)\s*</tool_call>'
    ]
    for pattern in tool_patterns:
        for match in re.finditer(pattern, content, re.DOTALL):
            try:
                tool_content = match.group(1).strip()
                # 使用安全JSON解析（简化版本）
                tool_data = text_parser._safe_json_parse(tool_content)
                tool_name = tool_data.get("name", "unknown")
                
                parsed_elements.append({
                    "type": "tool_call",
                    "tool_name": tool_name,
                    "reasoning": tool_data.get("reasoning", ""),
                    "args": tool_data.get("args", {}),
                    "start": match.start(),
                    "end": match.end()
                })
                
                # 替换为友好显示
                if "transfer_to" in tool_name:
                    formatted_content = formatted_content.replace(match.group(0), 
                        f"🔧 正在转交任务...")
                else:
                    formatted_content = formatted_content.replace(match.group(0), 
                        f"🔧 正在使用 {tool_name}...")
                    
            except (json.JSONDecodeError, Exception):
                # 如果JSON解析失败，保持原内容
                continue
    
    # 3. 智能体选择 [AGENT_SELECTION]...[/AGENT_SELECTION]
    agent_patterns = [
        r'\[AGENT_SELECTION\]\s*(.*?)\s*\[/AGENT_SELECTION\]',
        r'<agent_selection>\s*(.*?)\s*</agent_selection>'
    ]
    for pattern in agent_patterns:
        for match in re.finditer(pattern, content, re.DOTALL):
            try:
                agent_content = match.group(1).strip()
                agent_data = text_parser._safe_json_parse(agent_content)
                agent_name = agent_data.get("agent", "unknown")
                
                parsed_elements.append({
                    "type": "agent_selection", 
                    "agent": agent_name,
                    "reasoning": agent_data.get("reasoning", ""),
                    "task": agent_data.get("task", ""),
                    "start": match.start(),
                    "end": match.end()
                })
                
                # 替换为友好显示
                friendly_agent_names = {
                    "knowledge_agent": "知识专家", "simple_chat": "通用助手",
                    "mcp_agent": "工具专家", "multimodal_agent": "多模态专家"
                }
                display_name = friendly_agent_names.get(agent_name, agent_name)
                formatted_content = formatted_content.replace(match.group(0),
                    f"📋 将任务分配给 {display_name}")
                    
            except (json.JSONDecodeError, Exception):
                continue
    
    # 4. 任务转交 [HANDOFF]...[/HANDOFF] - 支持多行格式
    handoff_patterns = [
        r'\[HANDOFF\]\s*\n?\s*agent:\s*(\w+)\s*\n?\s*task:\s*([^\[]*?)\s*\[/HANDOFF\]',  # 多行格式
        r'\[HANDOFF\]\s*agent:\s*(\w+)\s*task:\s*([^\[]*?)\[/HANDOFF\]',  # 单行格式
        r'<handoff>\s*agent:\s*(\w+)\s*task:\s*([^<]*)</handoff>'
    ]
    for pattern in handoff_patterns:
        for match in re.finditer(pattern, content, re.DOTALL):
            agent_name = match.group(1).strip()
            task = match.group(2).strip()
            
            parsed_elements.append({
                "type": "handoff",
                "target_agent": agent_name,
                "task": task,
                "start": match.start(),
                "end": match.end()
            })
            
            # 直接移除handoff内容，不显示转交信息
            formatted_content = formatted_content.replace(match.group(0), "")
    
    # 5. 引用标记 [CITE]...[/CITE]
    cite_pattern = r'\[CITE\]([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|(.*?)\[/CITE\]'
    cite_counter = 1
    for match in re.finditer(cite_pattern, content):
        doc_name, chunk_id, similarity_score, chunk_index, original_text = match.groups()
        
        parsed_elements.append({
            "type": "cite",
            "cite_id": cite_counter,
            "doc_name": doc_name.strip(),
            "chunk_id": chunk_id.strip(),
            "similarity_score": float(similarity_score) if similarity_score.replace('.', '').isdigit() else 0,
            "chunk_index": int(chunk_index) if chunk_index.isdigit() else 0,
            "original_text": original_text.strip(),
            "start": match.start(),
            "end": match.end()
        })
        
        # 替换为引用格式
        formatted_content = formatted_content.replace(match.group(0), f"[{cite_counter}]")
        cite_counter += 1
    
    # 6. 处理任务完成标记
    task_complete_patterns = [
        r'\[TASK_COMPLETE\]\s*',  # [TASK_COMPLETE]
        r'<task_complete\s*/?\s*>',  # <task_complete>
        r'\[/TASK_COMPLETE\]\s*'  # [/TASK_COMPLETE]
    ]
    for pattern in task_complete_patterns:
        formatted_content = re.sub(pattern, '', formatted_content)
    
    # 7. 清理其他监督者内部标记
    internal_patterns = [
        r'\[AGENT_START\].*?\[/AGENT_START\]',  # Agent开始标记
        r'\[AGENT_END\].*?\[/AGENT_END\]',      # Agent结束标记
    ]
    for pattern in internal_patterns:
        formatted_content = re.sub(pattern, '', formatted_content, flags=re.DOTALL)
    
    # 8. 清理多余的空白行
    formatted_content = re.sub(r'\n\s*\n\s*\n', '\n\n', formatted_content).strip()
    
    return parsed_elements, formatted_content

@router.post("/jiliang/chat/get_history")
async def get_thread_history(request: Request, body: ThreadHistoryRequest):
    """
    获取线程历史消息接口
    
    使用LangGraph的时间回溯能力，从多智能体系统中获取完整的对话历史
    """

    # 确保 checkpointer 已初始化
    if not checkpointer_initialized:
        await initialize_checkpointer()

    try:
        logger.info(f"获取线程历史 - 用户: {body.user_id}, 会话: {body.qa_id}")
        
        # 确保多智能体服务已初始化
        from rag_chat.multi_agent_service import get_multi_agent_service, initialize_multi_agent_service
        
        multi_agent_service = get_multi_agent_service()
        if not multi_agent_service.initialized:
            # 初始化多智能体服务
            await initialize_multi_agent_service(
                checkpointer=graph_checkpointer,
                mcp_router=mcp_router,
                mcp_simple_router=mcp_simple_router
            )
        
        # 保持原来的thread_id格式
        thread_id = f"{body.user_id}--{body.qa_id}"
        
        # 使用multi_agent_service的graph来获取状态历史
        graph = multi_agent_service.text_graph or multi_agent_service.graph
        
        if not graph or not hasattr(graph, 'checkpointer') or not graph.checkpointer:
            logger.error("Graph checkpointer不可用")
            return JSONResponse(
                content={"error": "检查点系统不可用"},
                status_code=500
            )
        
        # 构造LangGraph配置（不使用命名空间）
        config = {
            "configurable": {
                "thread_id": thread_id
            }
        }
        
        try:
            # 使用LangGraph的get_state_history方法获取完整历史
            state_history = []
            try:
                # 尝试异步方法
                if hasattr(graph, 'aget_state_history'):
                    async for state_snapshot in graph.aget_state_history(config):
                        state_history.append(state_snapshot)
                else:
                    # 同步方法
                    for state_snapshot in graph.get_state_history(config):
                        state_history.append(state_snapshot)
            except Exception as history_error:
                logger.debug(f"获取状态历史失败: {history_error}")
                # 尝试使用当前状态作为备份
                try:
                    if hasattr(graph, 'aget_state'):
                        current_state = await graph.aget_state(config)
                    else:
                        current_state = graph.get_state(config)
                    if current_state:
                        state_history = [current_state]
                except Exception as state_error:
                    logger.error(f"获取当前状态也失败: {state_error}")
                    raise history_error
            
            # 如果没有找到任何历史记录
            if not state_history:
                logger.warning(f"未找到线程状态: {thread_id}")
                return ThreadHistoryResponse(
                    thread_id=thread_id,
                    messages=[],
                    total_count=0,
                    metadata={"status": "no_history", "thread_id": thread_id}
                )
            
            logger.info(f"找到 {len(state_history)} 个状态快照")
            
            # 使用最新状态快照（包含完整对话历史）
            latest_state = state_history[-1]
            
            # 从最新状态中提取主要对话消息
            main_messages = []
            if latest_state.values and 'messages' in latest_state.values:
                for msg in latest_state.values['messages']:
                    message_dict = _convert_langgraph_message_to_display_format(msg, latest_state)
                    if message_dict:
                        main_messages.append(message_dict)
            
            # 应用限制
            if body.limit and len(main_messages) > body.limit:
                main_messages = main_messages[-body.limit:]  # 取最近的N条消息
            
            # 构造元数据
            metadata = {
                "thread_id": thread_id,
                "total_snapshots": len(state_history),
                "status": "success"
            }
            
            if body.include_metadata:
                metadata.update({
                    "graph_type": "text_graph" if multi_agent_service.text_graph else "function_graph",
                    "latest_checkpoint_id": state_history[-1].config.get("configurable", {}).get("checkpoint_id") if state_history else None
                })
            
            logger.info(f"成功获取线程历史 - 线程: {thread_id}, 消息数: {len(main_messages)}")
            
            return ThreadHistoryResponse(
                thread_id=thread_id,
                messages=main_messages,
                total_count=len(main_messages),
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"从graph获取线程状态失败: {e}")
            return JSONResponse(
                content={"error": f"获取线程历史失败: {str(e)}"},
                status_code=500
            )
            
    except Exception as e:
        logger.error(f"获取线程历史接口异常: {e}")
        return JSONResponse(
            content={"error": f"系统异常: {str(e)}"},
            status_code=500
        )
