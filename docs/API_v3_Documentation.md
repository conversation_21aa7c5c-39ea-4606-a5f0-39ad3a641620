# 多智能体聊天API v3接口文档

## 接口概述

多智能体聊天API v3是基于LangGraph的企业级智能对话系统，采用统一多智能体架构，支持多模态输入、多文件上传、MCP工具集成和实时流式响应。系统默认运行在ReAct模式下，提供推理和行动的完整循环。

## 接口信息

- **接口路径**: `/jiliang/chat/v3`
- **请求方法**: `POST`
- **内容类型**: `multipart/form-data` (支持文件上传和表单参数)
- **响应类型**: `text/event-stream; charset=utf-8` (SSE流式响应)
- **执行模式**: `react_mode` (固定，支持推理和行动循环)

## 请求参数

### 请求头 (Headers)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `x-api-key` | String | 是 | API密钥，用于身份验证和权限控制 |
| `x-username` | String | 是 | 用户标识，用于个性化服务和会话隔离 |
| `x-uri` | String | 否 | 请求URI，用于审计日志记录 |

### Json Parameters

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `question` | String | 是 | - | 用户问题或指令内容 |
| `qa_id` | String | 否 | 自动生成UUID | 对话会话ID，用于会话管理和状态恢复 |
| `mcp_ids` | List | 否 | [] | MCP外部工具ID列表 |
| `extral_rag_ids` | List | 否 | [] | 企业自定义RAG知识库ID列表 |
| `files_ids` | List | 否 | [] | 支持多文件上传 |
| `images_ids` | List | 否 | [] | 支持多图片上传 |

**支持的文件类型**:
- **文档**: PDF, DOCX, DOC, TXT, MD, CSV, XLSX, XLS, PPT, PPTX
- **图片**: JPG, JPEG, PNG, GIF, BMP, WEBP, SVG

## 响应格式

### 响应头 (Response Headers)

| 参数名 | 值 | 说明 |
|--------|-----|------|
| `Qa-Id` | {qa_id} | 本次对话的会话ID |
| `Agent-System` | MultiAgent-v3 | 系统版本标识 |
| `AI-Assistant` | {warn_encoder} | AI助手标识和编码提示 |

### 响应体 (SSE事件流)

响应采用Server-Sent Events (SSE) 格式，支持实时流式输出：

```
data: {"type": "event_type", "data": {...}}

```

### 完整SSE事件类型 (18种)

#### 1. 系统状态事件 (3种)

**status** - 系统状态通知
```json
{
  "type": "status",
  "data": {
    "message": "开始处理多智能体问答"
  }
}
```

**字段说明：**
- `message`: 系统状态描述信息

**completion** - 处理完成统计（包含token使用量）- **已修复多Agent场景下的token统计问题**
```json
{
  "type": "completion",
  "data": {
    "message": "多智能体问答处理完成",
    "execution_mode": "react_mode",
    "agents_used": ["intent_analyzer", "multimodal_agent"],
    "tools_called": ["search_document_with_rag", "search_knowledge_base"],
    "custom_rag_used": true,
    "token_usage": {
      "total_tokens": 12543,
      "prompt_tokens": 8234,
      "completion_tokens": 4309,
      "total_calls": 8,
      "agent_breakdown": {
        "intent_analyzer": {"prompt_tokens": 1200, "completion_tokens": 800, "total_tokens": 2000},
        "multimodal_agent": {"prompt_tokens": 3500, "completion_tokens": 2100, "total_tokens": 5600},
        "knowledge_agent": {"prompt_tokens": 3534, "completion_tokens": 1409, "total_tokens": 4943}
      },
      "model_breakdown": {
        "qwen2.5-72b": {"prompt_tokens": 5500, "completion_tokens": 3200, "total_tokens": 8700},
        "deepseek-chat": {"prompt_tokens": 2734, "completion_tokens": 1109, "total_tokens": 3843}
      },
      "call_breakdown": {
        "by_agent": {"intent_analyzer": 1, "multimodal_agent": 3, "knowledge_agent": 4},
        "by_model": {"qwen2.5-72b": 5, "deepseek-chat": 3}
      }
    }
  }
}
```

**字段说明：**
- `message`: 处理完成的描述信息
- `execution_mode`: 执行模式（如"react_mode"、"function_call"、"text_parsing"）
- `agents_used`: 本次对话中使用的智能体列表
- `tools_called`: 本次对话中调用的工具列表
- `custom_rag_used`: 是否使用了自定义RAG知识库
- `token_usage`: 详细的token使用统计
  - `total_tokens`: 总token数量
  - `prompt_tokens`: 输入token数量
  - `completion_tokens`: 输出token数量
  - `total_calls`: 总API调用次数
  - `agent_breakdown`: 按智能体的token统计
  - `model_breakdown`: 按模型的token统计
  - `call_breakdown`: 调用次数分解统计

**error** - 错误信息
```json
{
  "type": "error",
  "data": "具体错误描述信息"
}
```

**字段说明：**
- `data`: 错误描述信息（字符串类型）

#### 2. Agent生命周期事件 (2种)

**agent_start** - Agent开始处理
```json
{
  "type": "agent_start",
  "data": {
    "agent_name": "multimodal_agent",
    "namespace": "multimodal_agent:uuid"
  }
}
```

**字段说明：**
- `agent_name`: 开始处理的智能体名称
- `namespace`: 智能体的命名空间标识（包含UUID）

**agent_complete** - Agent处理完成
```json
{
  "type": "agent_complete",
  "data": {
    "agent_name": "multimodal_agent",
    "namespace": "multimodal_agent:uuid"
  }
}
```

**字段说明：**
- `agent_name`: 完成处理的智能体名称
- `namespace`: 智能体的命名空间标识（包含UUID）

#### 3. 思考相关事件 (3种)

**thinking_start** - 开始思考阶段
```json
{
  "type": "thinking_start",
  "data": {
    "agent_name": "multimodal_agent",
    "namespace": "multimodal_agent:uuid"
  }
}
```

**字段说明：**
- `agent_name`: 开始思考的智能体名称
- `namespace`: 智能体的命名空间标识（包含UUID）

**thinking** - 思考过程实时流式输出
```json
{
  "type": "thinking",
  "data": {
    "agent_name": "multimodal_agent",
    "content": "💭 用户上传了一个PDF文档，我需要分析其内容结构。首先检查文档类型和页数..."
  }
}
```

**字段说明：**
- `agent_name`: 正在思考的智能体名称
- `content`: 思考过程的实时内容

**thinking_complete** - 思考阶段完成
```json
{
  "type": "thinking_complete",
  "data": {
    "agent_name": "multimodal_agent",
    "namespace": "multimodal_agent:uuid"
  }
}
```

**字段说明：**
- `agent_name`: 完成思考的智能体名称
- `namespace`: 智能体的命名空间标识（包含UUID）

#### 4. 内容输出事件 (1种)

**content** - 主要内容流式输出
```json
{
  "type": "content",
  "data": {
    "agent_name": "multimodal_agent",
    "content": "根据您上传的文档，我可以为您总结以下要点：\n\n1. "
  }
}
```

**字段说明：**
- `agent_name`: 输出内容的智能体名称
- `content`: 流式输出的内容片段

#### 5. 工具相关事件 (2种)

**tool_call** - 工具调用通知
```json
{
  "type": "tool_call",
  "data": {
    "agent_name": "multimodal_agent",
    "action": "🔍 正在分析您上传的文档...",
    "tool_name": "search_document_with_rag"
  }
}
```

**字段说明：**
- `agent_name`: 调用工具的智能体名称
- `action`: 用户友好的工具调用描述
- `tool_name`: 被调用的工具技术名称

**tool_result** - 工具执行结果（完整信息）
```json
{
  "type": "tool_result",
  "data": {
    "agent_name": "multimodal_agent",
    "tool_call_id": "call_search_123",
    "tool_name": "search_document_with_rag",
    "display_name": "文档智能问答",
    "content": "根据文档内容分析，系统架构包含以下核心组件...",
    "truncated_content": "根据文档内容分析，系统架构包含以下核心组件...",
    "content_length": 1250,
    "timestamp": "2025-01-23T10:30:45Z",
    "is_error": false
  }
}
```

**字段说明：**
- `agent_name`: 执行工具的智能体名称
- `tool_call_id`: 工具调用的唯一标识
- `tool_name`: 工具的技术名称
- `display_name`: 用户友好的工具显示名称
- `content`: 工具执行的完整结果内容
- `truncated_content`: 截断的内容（前500字符，用于预览）
- `content_length`: 完整内容的字符长度
- `timestamp`: 执行时间戳
- `is_error`: 是否为错误结果

#### 6. Agent协作事件 (2种)

**agent_assignment** - Agent任务分配
```json
{
  "type": "agent_assignment",
  "data": {
    "agent_name": "intent_analyzer",
    "action": "📋 将任务分配给 多模态专家",
    "target_agent": "multimodal_agent",
    "task": "文档内容分析和总结"
  }
}
```

**字段说明：**
- `agent_name`: 分配任务的智能体名称
- `action`: 用户友好的任务分配描述
- `target_agent`: 接收任务的目标智能体名称
- `task`: 任务描述

**agent_handoff** - Agent任务转交
```json
{
  "type": "agent_handoff",
  "data": {
    "agent_name": "multimodal_agent",
    "action": "↗️ 正在转交给 知识专家...",
    "target_agent": "knowledge_agent"
  }
}
```

**字段说明：**
- `agent_name`: 转交任务的智能体名称
- `action`: 用户友好的任务转交描述
- `target_agent`: 接收任务的目标智能体名称

#### 7. 特殊功能事件 (5种)

**intent_analysis** - 意图分析结果格式化输出
```json
{
  "type": "intent_analysis",
  "data": {
    "agent_name": "intent_analyzer",
    "namespace": "main",
    "formatted_result": "📋 **任务规划完成**\n\n🎯 **处理模式**: 单一代理模式\n👤 **主要负责**: 多模态专家\n📊 **复杂度**: 中等\n🎯 **置信度**: 95%\n\n✅ 开始执行任务...",
    "raw_data": {
      "processing_mode": "single_agent",
      "primary_agent": "multimodal_agent",
      "secondary_agents": [],
      "complexity": "medium",
      "confidence": 0.95,
      "reasoning": "用户上传了文档需要分析",
      "estimated_duration": 30
    }
  }
}
```

**字段说明：**
- `agent_name`: 进行意图分析的智能体名称
- `namespace`: 智能体的命名空间标识
- `formatted_result`: 格式化后的用户友好显示结果
- `raw_data`: 原始分析数据对象
  - `processing_mode`: 处理模式（如"single_agent"、"supervisor_mode"等）
  - `primary_agent`: 主要负责的智能体
  - `secondary_agents`: 协作智能体列表
  - `complexity`: 任务复杂度（"low"、"medium"、"high"）
  - `confidence`: 分析置信度（0-1之间的数值）
  - `reasoning`: 分析推理过程
  - `estimated_duration`: 预计执行时间（秒）

**context_summary_start** - 上下文总结开始
```json
{
  "type": "context_summary_start",
  "data": {
    "agent_name": "system_context_summarizer",
    "message": "正在智能总结历史对话上下文..."
  }
}
```

**字段说明：**
- `agent_name`: 执行上下文总结的智能体名称
- `message`: 总结开始的提示信息

**context_summary_complete** - 上下文总结完成
```json
{
  "type": "context_summary_complete",
  "data": {
    "agent_name": "system_context_summarizer",
    "message": "上下文总结完成，继续处理您的问题"
  }
}
```

**字段说明：**
- `agent_name`: 执行上下文总结的智能体名称
- `message`: 总结完成的提示信息

**human_message** - 人类消息回显
```json
{
  "type": "human_message",
  "data": {
    "content": "请帮我分析这份技术文档"
  }
}
```

**字段说明：**
- `content`: 用户输入的原始消息内容

**cite** - 文档来源引用
```json
{
  "type": "cite",
  "data": {
    "agent_name": "multimodal_agent",
    "cite_id": "1",
    "doc_name": "AI服务接口文档.pdf",
    "chunk_id": "chunk_003",
    "similarity_score": 0.967,
    "original_text": "根据技术文档分析，该系统主要包含以下核心组件",
    "display_text": "[1]",
    "raw_content": "API网关层负责请求路由和负载均衡，服务层处理业务逻辑..."
  }
}
```

**字段说明：**
- `agent_name`: 生成引用的智能体名称
- `cite_id`: 引用的唯一标识符
- `doc_name`: 源文档名称
- `chunk_id`: 文档块标识
- `similarity_score`: 相似度评分（0-1之间的数值）
- `original_text`: 引用的原始文本
- `display_text`: 显示用的引用标记（如"[1]"）
- `raw_content`: 完整的原始文档内容片段

## 请求示例

### 基础文本对话

```bash
curl -X POST "http://localhost:18800/jiliang/chat/v3" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -H "x-username: user123" \
  -d '{
    "question": "请介绍一下三峡集团的清洁能源业务发展情况",
    "model": "deepseek-chat",
    "temperature": 0.7
  }'
```

### 多文件、图片上传对话

```bash
curl -X POST "http://localhost:18800/jiliang/chat/v3" \
  -H "x-api-key: your-api-key" \
  -H "x-username: user123" \
  -F "question=请帮我分析这些文档的核心内容并生成总结报告" \
  -F "files=@technical_report.pdf" \
  -F "files=@chart_image.png" \
  -F "files=@data_analysis.xlsx" \
  -F "images=@chart_image.png" \
  -F "model=deepseek-chat"
```

### 启用MCP外部工具

```bash
curl -X POST "http://localhost:18800/jiliang/chat/v3" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -H "x-username: user123" \
  -d '{
    "question": "查询今天北京的天气情况并推荐合适的户外活动",
    "mcp_ids": ["weather_api", "activity_recommender"],
    "model": "deepseek-chat"
  }'
```

### 企业自定义RAG知识库查询

```bash
curl -X POST "http://localhost:18800/jiliang/chat/v3" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -H "x-username: user123" \
  -d '{
    "question": "根据公司的安全规范，在数据中心操作时需要注意哪些事项？",
    "extral_rag_ids": ["company_security_policies", "datacenter_procedures"],
    "model": "deepseek-chat"
  }'
```

### 文档问答与来源引用（CITE功能演示）

```bash
curl -X POST "http://localhost:18800/jiliang/chat/v3" \
  -H "x-api-key: your-api-key" \
  -H "x-username: user123" \
  -F "question=帮我总结这个技术文档的核心要点，需要详细的来源信息" \
  -F "files=@technical_specification.pdf" \
  -F "model=deepseek-chat"
```

**预期响应示例**（包含CITE事件）：
```
# SSE事件流

data: {"type": "agent_start", "data": {"agent_name": "multimodal_agent", "namespace": "main"}}

data: {"type": "tool_call", "data": {"agent_name": "multimodal_agent", "action": "🔍 正在分析您上传的文档...", "tool_name": "search_document_with_rag"}}

data: {"type": "content", "data": {"agent_name": "multimodal_agent", "content": "根据技术文档分析，该系统主要包含以下核心组件："}}

data: {"type": "cite", "data": {"agent_name": "multimodal_agent", "doc_name": "technical_specification.pdf", "chunk_id": "chunk_001", "similarity_score": 0.985, "position": "1/25", "display_text": "📄 technical_specification.pdf | 🔖 chunk_001 | 📊 0.985 | 📍 1/25"}}

data: {"type": "content", "data": {"agent_name": "multimodal_agent", "content": " API网关层负责请求路由和负载均衡"}}

data: {"type": "cite", "data": {"agent_name": "multimodal_agent", "doc_name": "technical_specification.pdf", "chunk_id": "chunk_005", "similarity_score": 0.967, "position": "5/25", "display_text": "📄 technical_specification.pdf | 🔖 chunk_005 | 📊 0.967 | 📍 5/25"}}

data: {"type": "completion", "data": {"message": "文档分析完成", "execution_mode": "react_mode", "agents_used": ["multimodal_agent"], "tools_called": ["search_document_with_rag"]}}
```

## 错误处理和状态码

### HTTP状态码

| 状态码 | 说明 | 场景 |
|--------|------|------|
| 200 | 成功 | 正常SSE流式响应 |
| 400 | 请求错误 | 参数错误、文件超限等 |
| 401 | 认证失败 | API密钥无效或过期 |
| 500 | 服务器错误 | 内部系统异常 |

### SSE错误事件

```json
{
  "type": "error",
  "data": "上传文件总大小超过限制 (20.5MB > 15.0MB)"
}
```

### 常见错误场景

1. **文件大小超限**
```json
{
  "type": "error", 
  "data": "上传文件总大小超过限制 (16.2MB > 15.0MB)"
}
```

2. **多智能体服务未初始化**
```json
{
  "type": "error",
  "data": "多智能体服务未初始化"
}
```

3. **文件处理失败**
```json
{
  "type": "error",
  "data": "文件处理失败: 不支持的文件格式"
}
```
